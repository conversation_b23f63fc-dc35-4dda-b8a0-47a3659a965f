package com.workplat.electronic.certificate;
//package com.workplat.gss.script.biz.loader;

import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.common.script.service.instance.FormFillBackLoader;
import com.workplat.gss.common.script.model.instance.FormFillBackInput;
import com.workplat.gss.common.script.model.instance.FormFillBackOutput;
import com.google.common.collect.ImmutableMap;
import com.workplat.gss.application.dubbo.entity.BizInstanceQuota;
import com.workplat.gss.application.dubbo.service.BizInstanceQuotaService;
import com.workplat.gss.common.core.context.ApplicationContextUtil;
import com.workplat.gss.common.script.model.instance.FormFillBackInput;
import com.workplat.gss.common.script.model.instance.FormFillBackOutput;
import com.workplat.gss.common.script.service.instance.FormFillBackLoader;

import java.util.HashMap;
import java.util.Map;

public class FormFillBackLoaderImpl implements FormFillBackLoader{
    @Override
    public FormFillBackOutput formFillBack(FormFillBackInput input) {
        BizInstanceQuotaService bizInstanceQuotaService = ApplicationContextUtil.getBean(BizInstanceQuotaService.class);
        BizInstanceQuota quota =  bizInstanceQuotaService.queryForSingle(ImmutableMap.of("=(instance.id)", input.getInstanceId(), "=(title)", "经营场所的用房性质"));
        String optionValue = quota.getOptions().get(0).getLabel();
        FormFillBackOutput output = new FormFillBackOutput();

        Map<String, String> formMap = new HashMap<>();

        if ("自有房产".equals(optionValue)){
            formMap.put("bgyfxz", "自有房产");
        } else if ("有偿租赁".equals(optionValue)){
            formMap.put("bgyfxz", "有偿租赁");
        }

        // 经办人
        BizInstanceInfoService bizInstanceInfoService = ApplicationContextUtil.getBean(BizInstanceInfoService.class);
        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(input.getInstanceId());
        formMap.put("jbr", bizInstanceInfo.getApplicationName());
        formMap.put("jbrxm", bizInstanceInfo.getLinkName());
        formMap.put("jbrsjhm", bizInstanceInfo.getLinkPhone());
        formMap.put("jbrsfzh", bizInstanceInfo.getLinkCertificateCode());

        output.setFormMap(formMap);
        return output;
    }
}