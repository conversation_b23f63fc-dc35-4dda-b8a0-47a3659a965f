package com.workplat.accept.business.chat.service.Impl;

import com.google.common.collect.ImmutableMap;
import com.workplat.accept.business.chat.convert.BizChatConversationVOConvert;
import com.workplat.accept.business.chat.dto.BizChatConversationDTO;
import com.workplat.accept.business.chat.dto.DeleteConversationDTO;
import com.workplat.accept.business.chat.entity.BizChatConversation;
import com.workplat.accept.business.chat.service.BizChatConversationService;
import com.workplat.accept.business.chat.vo.BizChatConversationVO;
import com.workplat.gss.common.core.dto.PageableDTO;
import com.workplat.gss.common.core.response.ResponseData;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ChatConversationService {

    private final BizChatConversationService bizChatConversationService;

    private final BizChatConversationVOConvert bizChatConversationVOConvert;


    public ChatConversationService(BizChatConversationService bizChatConversationService, BizChatConversationVOConvert bizChatConversationVOConvert) {
        this.bizChatConversationService = bizChatConversationService;
        this.bizChatConversationVOConvert = bizChatConversationVOConvert;
    }

    public BizChatConversation createConversation(String userId, String title, String channel) {
        BizChatConversation conversation = BizChatConversation.builder()
                .userId(userId)
                .title(title)
                .channel(channel)
                .build();
        return bizChatConversationService.createConversation(conversation);
    }

    public void updateConversation(BizChatConversation conversation) {
        bizChatConversationService.save(conversation);
    }

    public void deleteConversation(DeleteConversationDTO deleteConversationDTO) {
        bizChatConversationService.deleteByIds(deleteConversationDTO.getConversationIds());
    }

    public Page<BizChatConversationVO> getConversationPage(BizChatConversationDTO dto, PageableDTO pageable) {
        Page<BizChatConversation> bizChatConversations =
                bizChatConversationService.queryForPage(ImmutableMap.of("=(userId)", dto.getUserId(), "=(channel)", dto.getChannel()),
                        pageable.convertPageable());
        return bizChatConversationVOConvert.convert(bizChatConversations);
    }

    public BizChatConversationVO getLatestConversation(BizChatConversationDTO dto) {
        PageableDTO pageable = PageableDTO.builder().pageNo(1).pageSize(1).sort(new String[]{"updateTime"}).direction(new String[]{"DESC"}).build();
        Page<BizChatConversation> bizChatConversations =
                bizChatConversationService.queryForPage(ImmutableMap.of("=(userId)", dto.getUserId(),
                        "=(channel)", dto.getChannel()), pageable.convertPageable());
        BizChatConversationVO first = bizChatConversationVOConvert.convert(bizChatConversations).getContent().getFirst();
        // 判断是否是近2小时的会话
        if (first != null && first.getUpdateTime().getTime() > System.currentTimeMillis() - 2 * 60 * 60 * 1000) {
            return first;
        }
        return null;
    }

    public BizChatConversation getConversation(String recordId) {
        return bizChatConversationService.queryForSingle(ImmutableMap.of("=(id)", recordId));
    }
}