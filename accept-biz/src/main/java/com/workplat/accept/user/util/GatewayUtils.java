package com.workplat.accept.user.util;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSONObject;
import com.workplat.gateway.client.GatewayClientRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;



/**
 * @Author: Odin
 * @Date: 2024/9/27 19:07
 * @Description:
 */

@Slf4j
@Component
public class GatewayUtils {

    private static String allinoneUrlPrefix = "https://zwfw.taicang.gov.cn/gateway-api/allinone-api";

    private static final GatewayClientRequest gatewayClientRequest = GatewayClientRequest.builder(

            "https://zwfw.taicang.gov.cn/gateway-api",

            "53C23A228B4C4FFB81A70E1D5C3C8283",

            "04f0aada158a4ed086a2df96787dff4d525166d566886f8b2b8b80973236bd85b9c8e585dcf8e9d461390afcf7957ca5b755c8b6e3809a7f686f98975bb990a982",

            "",

            ""

    );

    public static String getAllinoneApiResult(String url, Map<String, Object> formMap) {
        log.info("请求allinone接口：{}", allinoneUrlPrefix + url);
        String res = gatewayClientRequest.execute(HttpRequest.post(allinoneUrlPrefix + url).form(formMap), false, false);
        log.info("allinone接口 - {} -返回结果：{}", allinoneUrlPrefix + url, res);
        return res;
    }

    public static String executeRequest(String url, Map<String, String> header, Method method, String body,
                                 Map<String, Object> form, Boolean encrypt, Boolean verifySign) {
        HttpRequest httpRequest = HttpRequest.of(url);
        httpRequest.method(method);
        if (header != null && !header.isEmpty()) {
            for (Map.Entry<String, String> entry : header.entrySet()) {
                httpRequest.header(entry.getKey(), entry.getValue());
            }
        }
        if (StrUtil.isNotBlank(body)) {
            httpRequest.body(body);
        }
        if (form != null && !form.isEmpty()) {
            httpRequest.form(form);
        }
        try {
            String execute = gatewayClientRequest.execute(httpRequest, encrypt, verifySign);
            JSONObject jsonObject = JSONObject.parseObject(execute);
            if (jsonObject != null) {
                if (jsonObject.getJSONObject("data") != null) {
                    return jsonObject.getJSONObject("data").toString();
                }
                return jsonObject.toString();
            }
        } catch (Exception e) {
            log.error("请求失败", e);
        }
        return null;
    }

    public static void main(String[] args) {
        Map<String, Object> formMap = MapUtil.<String, Object>builder().put("idCard", "610431199905063016").put("name", "杨帆").build();
        String token = JSONUtil.parseObj(GatewayUtils.getAllinoneApiResult("/api/zzj/selfServiceLogin", formMap)).getStr("token");

        String allinoneApiResult = GatewayUtils.executeRequest("/api/ucenter/user/get",
                MapUtil.<String, String>builder().put(Header.AUTHORIZATION.getValue(), "Bearer " +  token).build(),
                Method.GET, null, null, false, false);
        System.out.println(allinoneApiResult);


    }

}
