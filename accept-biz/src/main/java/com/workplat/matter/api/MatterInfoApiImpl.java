package com.workplat.matter.api;

/**
 * <AUTHOR> cheng
 * @package com.workplat.matter.api
 * @description
 * @date 2025/5/21 16:38
 */

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.workplat.accept.business.serve.entity.BizServeInfo;
import com.workplat.accept.business.serve.entity.BizServeMethod;
import com.workplat.flow.entity.ConfFlow;
import com.workplat.flow.service.ConfFlowService;
import com.workplat.gss.common.core.exception.BusinessException;
import com.workplat.gss.common.core.response.ResponseData;
import com.workplat.gss.common.core.util.RedisUtil;
import com.workplat.gss.service.item.dubbo.matter.entity.ConfMatter;
import com.workplat.gss.service.item.dubbo.matter.service.ConfMatterService;
import com.workplat.matter.entity.ConfMatterExtend;
import com.workplat.matter.service.ConfMatterExtendService;
import com.workplat.matter.vo.ConfMatterExtendDTO;
import com.workplat.matter.vo.ConfMatterExtendVO;
import com.workplat.serve.dto.MethodDefaultDTO;
import com.workplat.serve.service.BizServeInfoService;
import com.workplat.serve.service.BizServeMethodService;
import org.springframework.transaction.annotation.Transactional;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
public class MatterInfoApiImpl implements MatterInfoApi{

    @Autowired
    private ConfMatterExtendService confMatterExtendService;
    @Autowired
    private ConfMatterService confMatterService;
    @Autowired
    private ConfFlowService confFlowService;
    @Autowired
    private BizServeInfoService serveInfoService;
    @Autowired
    private BizServeMethodService  serveMethodService;
    @Autowired
    private RedisUtil redisUtil;

    private static String serveRedisKey = "serve_method_default";


    @Override
    public ResponseData<ConfMatterExtendVO> getByName(String name) {
        ConfMatterExtend extend = confMatterExtendService.queryForSingle(MapUtil.<String, Object>builder()
                .put("=(matter.matterName)", name)
                .put("=(matter.isPublic)", "1")
                .build());
        if (extend == null){
            return ResponseData.success().build();
        }
        return ResponseData.success(convert(extend));
    }

    @Override
    public ResponseData<ConfMatterExtendVO> getByMatterId(String matterId) {
        ConfMatterExtend extend = confMatterExtendService.queryForSingle(MapUtil.<String, Object>builder()
                .put("=(matter.id)", matterId)
                .build());
        if (extend != null){
            return ResponseData.success(convert(extend));
        }
        ConfMatter matter = confMatterService.queryById(matterId);
        if (matter == null){
            throw new BusinessException("事项不存在");
        }
        ConfMatterExtendVO vo  = new ConfMatterExtendVO();
        vo.setMatterId(matterId);
        vo.setMatterName(matter.getMatterName());
        vo.setMatterCode(matter.getMatterCode());
        return ResponseData.success(new ConfMatterExtendVO());
    }

    @Override
    public ResponseData<ConfMatterExtendVO> getByMatterCode(String matterCode) {
        ConfMatterExtend extend = confMatterExtendService.queryForSingle(MapUtil.<String, Object>builder()
                .put("=(matter.matterCode)", matterCode)
                .build());
        if (extend != null){
            return ResponseData.success(convert(extend));
        }
        ConfMatter matter = confMatterService.queryForSingle(MapUtil.<String, Object>builder()
                .put("=(matterCode)", matterCode)
                .build());
        if (matter == null){
            throw new BusinessException("事项不存在");
        }
        ConfMatterExtendVO vo  = new ConfMatterExtendVO();
        vo.setMatterId(matter.getId());
        vo.setMatterName(matter.getMatterName());
        vo.setMatterCode(matter.getMatterCode());
        return ResponseData.success(new ConfMatterExtendVO());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseData<Void> saveExtend(ConfMatterExtendDTO dto) {
        ConfMatter matter = confMatterService.queryForSingle(MapUtil.<String, Object>builder().put("=(matterCode)", dto.getMatterCode()).build());
        if (matter == null){
            throw new BusinessException("事项不存在");
        }
        ConfFlow flow = confFlowService.queryForSingle(MapUtil.<String, Object>builder().put("=(code)", dto.getFlowCode()).build());
        if (flow == null){
            throw new BusinessException("流程不存在");
        }
        ConfMatterExtend extend = new ConfMatterExtend();
        ConfMatterExtend old = confMatterExtendService.queryForSingle(MapUtil.<String, Object>builder()
                .put("=(matter.matterCode)", dto.getMatterCode())
                .build());
        if (old != null){
            extend = old;
        }
        if (StringUtils.isNotBlank(dto.getInstructionRemoteTip())){
            extend.setInstructionRemoteTip(dto.getInstructionRemoteTip());
        }
        if (StringUtils.isNotBlank(dto.getInformedConsent())){
            extend.setInformedConsent(dto.getInformedConsent());
        }
        extend.setMatter(matter);
        extend.setConfFlow(flow);
        extend.setServeList(dto.getServeIds());
        extend.setInformAfterSubmit(dto.getInformAfterSubmit());
        confMatterExtendService.save(extend);
        // 作为事项保存到边聊边办服务中
        BizServeInfo serveInfo = serveInfoService.queryForSingle(MapUtil.<String, Object>builder().put("=(name)", matter.getMatterName()).build());
        if (serveInfo == null){
            serveInfo = new BizServeInfo();
            serveInfo.setCode(matter.getId());
            serveInfo.setName(matter.getMatterName());
            serveInfo.setType("item");
            serveInfo.setThirdParty(false);
            serveInfo.setEnable(true);

            BizServeMethod method = new BizServeMethod();
            method.setContent("我要办理" + matter.getMatterName());
            method.setType("LTB");
            method.setServe(serveInfo);

            // 从redis中获取服务方式默认值
            List<Object> list = redisUtil.lGet(serveRedisKey, 0, -1);
            if (list != null && !list.isEmpty()){
                List<MethodDefaultDTO> dtoList = JSONArray.parseArray(JSONArray.toJSONString(list), MethodDefaultDTO.class);
                for (MethodDefaultDTO methodDefaultDTO : dtoList) {
                    if ("LTB".equals(methodDefaultDTO.getMethod())){
                        method.setDescription(methodDefaultDTO.getDescription());
                        method.setIconId(methodDefaultDTO.getIconId());
                        break;
                    }
                }
            }
            serveMethodService.save(method);
            serveInfo.setMethodList(List.of(method));
            serveInfoService.save(serveInfo);
        }
        return ResponseData.success().build();
    }

    private ConfMatterExtendVO convert(ConfMatterExtend extend){
        ConfMatterExtendVO vo = new ConfMatterExtendVO();
        vo.setMatterName(extend.getMatter().getMatterName());
        vo.setFlowName(extend.getConfFlow().getName());
        vo.setFlowCode(extend.getConfFlow().getCode());
        vo.setMatterCode(extend.getMatter().getMatterCode());
        vo.setMatterId(extend.getMatter().getId());
        vo.setInformedConsent(extend.getInformedConsent());
        vo.setInformAfterSubmit(extend.getInformAfterSubmit());
        vo.setInstructionRemoteTip(extend.getInstructionRemoteTip());
        if (StringUtils.isNotBlank(extend.getServeList())){
            String[] serveIds = extend.getServeList().split(",");
            List<BizServeInfo> serveInfos =serveInfoService.queryByIds(serveIds);
            List<JSONObject> serveInfoVos = Lists.newArrayList();
            serveInfoVos.addAll(serveInfos.stream()
                    .map(serveInfo -> new JSONObject().fluentPut("serveId", serveInfo.getId()).fluentPut("serveName", serveInfo.getName()))
                    .toList());
            vo.setServeInfoVos(serveInfoVos);
        }
        return vo;
    }
}
