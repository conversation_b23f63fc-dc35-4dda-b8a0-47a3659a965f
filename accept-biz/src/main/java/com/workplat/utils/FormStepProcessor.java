package com.workplat.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FormStepProcessor {

    /**
     * 处理 JSON 表单 获取表单域的数量
     *
     * @param formJson 原始表单 JSON 字符串
     * @return 表单域的数量
     */
    public static int getFormStepCount(String formJson) {
        JSONObject result = new JSONObject();
        JSONObject formJsonObj = JSON.parseObject(formJson);

        if (formJsonObj.containsKey("renderList")) {
            JSONArray renderList = formJsonObj.getJSONArray("renderList");
            return renderList.size();
        }
        return 0;
    }

    /**
     * 处理 JSON 表单并根据步骤编号返回特定的 renderList 项
     *
     * @param formJson 原始表单 JSON 字符串
     * @param step     步数 （0， 1， 2）
     * @return JSONObject，其中包含请求的步骤数据或空对象（如果步骤无效）
     */
    public static JSONObject getStepData(String formJson, int step) {
        // 步长减一，因为数组索引从0开始
        int stepIndex = step - 1;
        JSONObject result = new JSONObject();
        JSONObject formJsonObj = JSON.parseObject(formJson);

        if (formJsonObj.containsKey("renderList")) {
            JSONArray renderList = formJsonObj.getJSONArray("renderList");

            if (stepIndex >= 0 && stepIndex < renderList.size()) {
                // Get the specific form area for the step
                JSONObject formArea = renderList.getJSONObject(stepIndex);

                // Create a new result object with the same structure but only the selected step
                result.put("tableName", formJsonObj.getString("tableName"));
                result.put("formAttribute", formJsonObj.getJSONObject("formAttribute"));
                result.put("beforeFunction", formJsonObj.getString("beforeFunction"));
                result.put("submitFunction", formJsonObj.getString("submitFunction"));

                // Create a new renderList with just the selected form area
                JSONArray singleStepRenderList = new JSONArray();
                singleStepRenderList.add(formArea);
                result.put("renderList", singleStepRenderList);
            }
        }

        return result;
    }

    /**
     * 返回指定步长之前的所有步长的替代版本
     *
     * @param formJson 原始表单 JSON 字符串
     * @param step     要包含的最大步数 （0， 1， 2）
     * @return JSONObject 包含指定步骤之前的所有步骤
     */
    public static JSONObject getStepsUpTo(String formJson, int step) {
        JSONObject result = new JSONObject();
        JSONObject formJsonObj = JSON.parseObject(formJson);

        if (formJsonObj.containsKey("renderList")) {
            JSONArray renderList = formJsonObj.getJSONArray("renderList");
            JSONArray filteredRenderList = new JSONArray();

            // Add all steps up to the specified one
            for (int i = 0; i <= step && i < renderList.size(); i++) {
                filteredRenderList.add(renderList.getJSONObject(i));
            }

            // Build the result object
            result.put("tableName", formJsonObj.getString("tableName"));
            result.put("formAttribute", formJsonObj.getJSONObject("formAttribute"));
            result.put("beforeFunction", formJsonObj.getString("beforeFunction"));
            result.put("submitFunction", formJsonObj.getString("submitFunction"));
            result.put("renderList", filteredRenderList);
        }

        return result;
    }

    /**
     * 判断是否还有字段可以填写
     * @param formJson 表单JSON字符串
     * @return boolean 是否还有字段可以填写
     */
    public static boolean hasFieldsToFill(String formJson) {
        JSONObject formJsonObj = JSON.parseObject(formJson);
        if (formJsonObj == null || !formJsonObj.containsKey("renderList")) {
            return false;
        }

        JSONArray renderList = formJsonObj.getJSONArray("renderList");
        if (renderList == null || renderList.isEmpty()) {
            return false;
        }

        // 递归检查是否有可编辑字段
        for (int i = 0; i < renderList.size(); i++) {
            JSONObject formArea = renderList.getJSONObject(i);
            if (hasFieldsInFormArea(formArea)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查表单域中是否有可填写的字段
     * @param formArea 表单域对象
     * @return boolean 是否有可填写的字段
     */
    private static boolean hasFieldsInFormArea(JSONObject formArea) {
        if (!formArea.containsKey("child")) {
            return false;
        }

        JSONArray children = formArea.getJSONArray("child");
        if (children == null || children.isEmpty()) {
            return false;
        }

        // 递归检查子组件
        for (int i = 0; i < children.size(); i++) {
            JSONObject child = children.getJSONObject(i);

            // 检查是否是可编辑的字段组件
            if (isEditableField(child)) {
                return true;
            }

            // 递归检查子组件的子元素
            if (hasFieldsInFormArea(child)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 判断组件是否是可编辑的字段
     * @param component 组件对象
     * @return boolean 是否是可编辑字段
     */
    private static boolean isEditableField(JSONObject component) {
        if (!component.containsKey("props")) {
            return false;
        }

        JSONObject props = component.getJSONObject("props");
        // 有field属性的组件认为是可编辑字段
        return props.containsKey("field") && props.getString("field") != null;
    }

    /**
     * 处理表单JSON，根据字段映射保留需要展示的表单项
     *
     * @param formJson 原始表单JSON字符串
     * @param fieldMap 字段值映射表
     * @return 处理后的表单JSON对象
     */
    public static JSONObject processFormJson(String formJson, Map<String, Object> fieldMap) {
        // 解析原始表单JSON
        JSONObject formJsonObj = JSON.parseObject(formJson);

        // 检查是否存在渲染列表
        if (formJsonObj.containsKey("renderList")) {
            JSONArray renderList = formJsonObj.getJSONArray("renderList");
            JSONArray filteredRenderList = new JSONArray();

            // 遍历每个表单区域
            for (int i = 0; i < renderList.size(); i++) {
                JSONObject formArea = renderList.getJSONObject(i);

                // 递归处理子组件
                if (formArea.containsKey("child")) {
                    JSONArray children = formArea.getJSONArray("child");
                    JSONArray filteredChildren = processChildrenRecursively(children, fieldMap);

                    // 只保留有匹配子组件的表单区域
                    if (!filteredChildren.isEmpty()) {
                        formArea.put("child", filteredChildren);
                        filteredRenderList.add(formArea);
                    }
                }
            }

            // 替换原始渲染列表为过滤后的列表
            formJsonObj.put("renderList", filteredRenderList);
        }

        return formJsonObj;
    }

    /**
     * 递归处理子组件，支持多层嵌套结构
     *
     * @param children 子组件数组
     * @param fieldMap 字段值映射表
     * @return 过滤后的子组件数组
     */
    private static JSONArray processChildrenRecursively(JSONArray children, Map<String, Object> fieldMap) {
        JSONArray filteredChildren = new JSONArray();

        for (int i = 0; i < children.size(); i++) {
            JSONObject child = children.getJSONObject(i);
            boolean shouldKeepChild = false;

            // 检查当前组件是否有field属性且在fieldMap中
            if (child.containsKey("props")) {
                JSONObject props = child.getJSONObject("props");
                String field = props.getString("field");

                if (field != null && fieldMap.containsKey(field)) {
                    // 更新字段值
                    Object value = fieldMap.get(field);
                    props.put("modelValue", value);
                    shouldKeepChild = true;
                }
            }

            // 递归处理子组件的子元素
            if (child.containsKey("child")) {
                JSONArray subChildren = child.getJSONArray("child");
                JSONArray filteredSubChildren = processChildrenRecursively(subChildren, fieldMap);

                if (!filteredSubChildren.isEmpty()) {
                    child.put("child", filteredSubChildren);
                    shouldKeepChild = true;
                }
            }

            // 如果当前组件或其子组件有匹配的字段，则保留
            if (shouldKeepChild) {
                filteredChildren.add(child);
            }
        }

        return filteredChildren;
    }

    public static void main(String[] args) {
        String string = "{\n" +
                "  \"tableName\" : \"\",\n" +
                "  \"renderList\" : [ {\n" +
                "    \"icon\" : \"icon-biaodan\",\n" +
                "    \"name\" : \"表单域\",\n" +
                "    \"platform\" : \"all\",\n" +
                "    \"type\" : \"formArea\",\n" +
                "    \"needSpecial\" : false,\n" +
                "    \"componentName\" : \"ANetFormArea\",\n" +
                "    \"props\" : {\n" +
                "      \"bgColor\" : \"#3A81FF\",\n" +
                "      \"title\" : \"身份证信息\",\n" +
                "      \"titleSize\" : 22,\n" +
                "      \"titleColor\" : \"#0E0D0D\",\n" +
                "      \"barColor\" : \"\",\n" +
                "      \"isHidden\" : false,\n" +
                "      \"functions\" : [ ],\n" +
                "      \"isShowButton\" : true,\n" +
                "      \"arrowColor\" : \"#000000\",\n" +
                "      \"show\" : true,\n" +
                "      \"field\" : \"f2BgEPZ1Q\"\n" +
                "    },\n" +
                "    \"events\" : { },\n" +
                "    \"child\" : [ ],\n" +
                "    \"rules\" : [ ],\n" +
                "    \"id\" : \"KfLjT25pj1\"\n" +
                "  } ],\n" +
                "  \"formAttribute\" : {\n" +
                "    \"inline\" : false,\n" +
                "    \"disabled\" : false,\n" +
                "    \"tableName\" : \"demoFormName\",\n" +
                "    \"labelWidth\" : \"100px\",\n" +
                "    \"labelPosition\" : \"right\"\n" +
                "  },\n" +
                "  \"beforeFunction\" : \"{}\",\n" +
                "  \"submitFunction\" : \"{}\"\n" +
                "}";
        System.out.println(hasFieldsToFill(string));
    }
}