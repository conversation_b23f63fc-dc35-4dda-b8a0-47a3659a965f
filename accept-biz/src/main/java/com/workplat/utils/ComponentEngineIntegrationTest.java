package com.workplat.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 组件引擎集成测试
 * 模拟 FieldShowComponentEngine 和 FormGroupComponentEngine 的使用场景
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-20
 */
public class ComponentEngineIntegrationTest {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    public static void main(String[] args) {
        try {
            // 模拟完整的表单数据（多个表单域）
            String fullFormJson = """
                {
                  "tableName": "testForm",
                  "renderList": [
                    {
                      "name": "表单域",
                      "type": "formArea",
                      "componentName": "ANetFormArea",
                      "props": {
                        "title": "身份证信息",
                        "field": "f2BgEPZ1Q"
                      },
                      "child": [
                        {
                          "name": "输入框",
                          "type": "input",
                          "componentName": "a-net-input",
                          "props": {
                            "field": "sqrxm",
                            "label": "申请人姓名"
                          }
                        },
                        {
                          "name": "单选框",
                          "type": "radio",
                          "componentName": "a-net-radio",
                          "props": {
                            "field": "xingbie",
                            "label": "性别"
                          }
                        }
                      ]
                    },
                    {
                      "name": "表单域",
                      "type": "formArea",
                      "componentName": "ANetFormArea",
                      "props": {
                        "title": "联系信息",
                        "field": "eiZooxlHx"
                      },
                      "child": [
                        {
                          "name": "输入框",
                          "type": "input",
                          "componentName": "a-net-input",
                          "props": {
                            "field": "sqrlxdh",
                            "label": "联系电话"
                          }
                        },
                        {
                          "name": "输入框",
                          "type": "input",
                          "componentName": "a-net-input",
                          "props": {
                            "field": "blsy",
                            "label": "办理事由"
                          }
                        }
                      ]
                    },
                    {
                      "name": "表单域",
                      "type": "formArea",
                      "componentName": "ANetFormArea",
                      "props": {
                        "title": "其他信息",
                        "field": "otherInfo"
                      },
                      "child": [
                        {
                          "name": "输入框",
                          "type": "input",
                          "componentName": "a-net-input",
                          "props": {
                            "field": "otherField",
                            "label": "其他字段"
                          }
                        }
                      ]
                    }
                  ]
                }
                """;

            System.out.println("=== 原始完整表单结构 ===");
            printFormStructure(fullFormJson);

            // 测试场景1：FieldShowComponentEngine - AI提取字段展示
            System.out.println("\n=== 场景1：FieldShowComponentEngine - AI提取字段展示 ===");
            String aiExtractFields = """
                {
                  "sqrxm": "张三",
                  "sqrlxdh": "13800138000"
                }
                """;
            
            System.out.println("AI提取的字段: " + aiExtractFields);
            
            FormFieldFilterUtil.FormFilterResult showResult = FormFieldFilterUtil.filterFormWithIndexTracking(
                    fullFormJson, aiExtractFields, true);
            
            System.out.println("FieldShow处理结果: " + showResult);
            System.out.println("筛选后的表单结构:");
            printFormStructure(showResult.getFilteredFormJson());

            // 测试场景2：FormGroupComponentEngine - 分步表单处理
            System.out.println("\n=== 场景2：FormGroupComponentEngine - 分步表单处理 ===");
            
            // 模拟已填写的字段
            String filledFields = """
                {
                  "sqrxm": "张三",
                  "xingbie": "男"
                }
                """;
            
            System.out.println("已填写的字段: " + filledFields);
            
            // 模拟分步处理：获取第1步数据
            String step1Data = FormStepProcessor.getStepData(fullFormJson, 1).toJSONString();
            System.out.println("\n第1步原始数据:");
            printFormStructure(step1Data);
            
            FormFieldFilterUtil.FormFilterResult step1Result = FormFieldFilterUtil.filterFormWithIndexTracking(
                    step1Data, filledFields, false);
            
            System.out.println("第1步处理结果: " + step1Result);
            System.out.println("第1步筛选后的表单结构:");
            printFormStructure(step1Result.getFilteredFormJson());
            
            // 检查是否需要跳到下一步
            boolean hasFields = FormStepProcessor.hasFieldsToFill(step1Result.getFilteredFormJson());
            System.out.println("第1步是否还有可填字段: " + hasFields);
            
            if (!hasFields) {
                System.out.println("\n第1步无可填字段，跳转到第2步");
                String step2Data = FormStepProcessor.getStepData(fullFormJson, 2).toJSONString();
                System.out.println("第2步原始数据:");
                printFormStructure(step2Data);
                
                FormFieldFilterUtil.FormFilterResult step2Result = FormFieldFilterUtil.filterFormWithIndexTracking(
                        step2Data, filledFields, false);
                
                System.out.println("第2步处理结果: " + step2Result);
                System.out.println("第2步筛选后的表单结构:");
                printFormStructure(step2Result.getFilteredFormJson());
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 打印表单结构概要
     */
    private static void printFormStructure(String formJson) throws Exception {
        JsonNode formNode = objectMapper.readTree(formJson);
        JsonNode renderList = formNode.get("renderList");
        
        if (renderList != null && renderList.isArray()) {
            System.out.println("表单域数量：" + renderList.size());
            for (int i = 0; i < renderList.size(); i++) {
                JsonNode item = renderList.get(i);
                printFormItem(item, 0, i);
            }
        } else {
            System.out.println("无表单域");
        }
    }
    
    /**
     * 递归打印表单项
     */
    private static void printFormItem(JsonNode item, int level, int index) {
        if (item == null) return;
        
        String indent = "  ".repeat(level);
        String name = item.has("name") ? item.get("name").asText() : "未知";
        String type = item.has("type") ? item.get("type").asText() : "";
        String componentName = item.has("componentName") ? item.get("componentName").asText() : "";
        
        String field = "";
        JsonNode propsNode = item.get("props");
        if (propsNode != null && propsNode.has("field")) {
            field = " [field: " + propsNode.get("field").asText() + "]";
        }
        
        String indexInfo = level == 0 ? " [索引: " + index + "]" : "";
        
        System.out.println(indent + "- " + name + " (" + componentName + ")" + 
                          (type.isEmpty() ? "" : " [type: " + type + "]") + field + indexInfo);
        
        // 递归打印子项
        JsonNode childNode = item.get("child");
        if (childNode != null && childNode.isArray()) {
            for (JsonNode child : childNode) {
                printFormItem(child, level + 1, -1);
            }
        }
    }
}
