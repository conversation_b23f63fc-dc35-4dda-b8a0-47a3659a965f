package com.workplat.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * 可新增表单（addTable）处理测试
 * 验证修复后的保留模式逻辑
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-20
 */
public class AddTableTest {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    public static void main(String[] args) {
        try {
            // 测试数据：包含可新增表单
            String formWithAddTable = """
                {
                  "tableName": "testForm",
                  "renderList": [
                    {
                      "name": "普通表单域",
                      "type": "formArea",
                      "componentName": "ANetFormArea",
                      "props": {
                        "title": "基本信息",
                        "field": "basicInfo"
                      },
                      "child": [
                        {
                          "name": "输入框",
                          "type": "input",
                          "componentName": "a-net-input",
                          "props": {
                            "field": "name",
                            "label": "姓名"
                          }
                        }
                      ]
                    },
                    {
                      "name": "可新增表单-有匹配字段",
                      "type": "addTable",
                      "componentName": "ANetAddTable",
                      "props": {
                        "title": "工作经历",
                        "field": "workExperience"
                      },
                      "child": [
                        {
                          "name": "输入框",
                          "type": "input",
                          "componentName": "a-net-input",
                          "props": {
                            "field": "company",
                            "label": "公司名称"
                          }
                        },
                        {
                          "name": "输入框",
                          "type": "input",
                          "componentName": "a-net-input",
                          "props": {
                            "field": "position",
                            "label": "职位"
                          }
                        }
                      ]
                    },
                    {
                      "name": "可新增表单-无匹配字段",
                      "type": "addTable",
                      "componentName": "ANetAddTable",
                      "props": {
                        "title": "教育经历",
                        "field": "education"
                      },
                      "child": [
                        {
                          "name": "输入框",
                          "type": "input",
                          "componentName": "a-net-input",
                          "props": {
                            "field": "school",
                            "label": "学校名称"
                          }
                        },
                        {
                          "name": "输入框",
                          "type": "input",
                          "componentName": "a-net-input",
                          "props": {
                            "field": "major",
                            "label": "专业"
                          }
                        }
                      ]
                    }
                  ]
                }
                """;

            // 字段映射：只包含部分字段
            String fieldMapData = """
                {
                  "name": "张三",
                  "company": "某某公司"
                }
                """;

            System.out.println("=== 可新增表单（addTable）处理测试 ===\n");

            System.out.println("原始表单结构:");
            printFormStructure(formWithAddTable);

            System.out.println("\n字段映射数据:");
            System.out.println("包含字段: name (姓名), company (公司名称)");

            // 测试保留模式
            System.out.println("\n=== 保留模式测试 ===");
            FormFieldFilterUtil.FormFilterResult keepResult = FormFieldFilterUtil.filterFormWithIndexTracking(
                    formWithAddTable, fieldMapData, true);
            
            System.out.println("处理结果: " + keepResult);
            System.out.println("被移除的表单域索引: " + keepResult.getRemovedFormAreaIndexes());
            System.out.println("被移除的表单域字段: " + keepResult.getRemovedFormAreaFields());
            
            System.out.println("\n筛选后的表单结构:");
            printFormStructure(keepResult.getFilteredFormJson());

            // 测试移除模式
            System.out.println("\n=== 移除模式测试 ===");
            FormFieldFilterUtil.FormFilterResult removeResult = FormFieldFilterUtil.filterFormWithIndexTracking(
                    formWithAddTable, fieldMapData, false);
            
            System.out.println("处理结果: " + removeResult);
            System.out.println("被移除的表单域索引: " + removeResult.getRemovedFormAreaIndexes());
            System.out.println("被移除的表单域字段: " + removeResult.getRemovedFormAreaFields());
            
            System.out.println("\n筛选后的表单结构:");
            printFormStructure(removeResult.getFilteredFormJson());

            // 测试边界情况：空字段映射
            System.out.println("\n=== 边界测试：空字段映射 ===");
            String emptyFieldMap = "{}";
            
            FormFieldFilterUtil.FormFilterResult emptyResult = FormFieldFilterUtil.filterFormWithIndexTracking(
                    formWithAddTable, emptyFieldMap, true);
            
            System.out.println("空字段映射保留模式结果: " + emptyResult);
            System.out.println("被移除的表单域数量: " + emptyResult.getRemovedFormAreaIndexes().size());

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 打印表单结构概要
     */
    private static void printFormStructure(String formJson) throws Exception {
        JsonNode formNode = objectMapper.readTree(formJson);
        JsonNode renderList = formNode.get("renderList");
        
        if (renderList != null && renderList.isArray()) {
            System.out.println("表单域数量：" + renderList.size());
            for (int i = 0; i < renderList.size(); i++) {
                JsonNode item = renderList.get(i);
                printFormItem(item, 0, i);
            }
        } else {
            System.out.println("无表单域");
        }
    }
    
    /**
     * 递归打印表单项
     */
    private static void printFormItem(JsonNode item, int level, int index) {
        if (item == null) return;
        
        String indent = "  ".repeat(level);
        String name = item.has("name") ? item.get("name").asText() : "未知";
        String type = item.has("type") ? item.get("type").asText() : "";
        String componentName = item.has("componentName") ? item.get("componentName").asText() : "";
        
        String field = "";
        JsonNode propsNode = item.get("props");
        if (propsNode != null && propsNode.has("field")) {
            field = " [field: " + propsNode.get("field").asText() + "]";
        }
        
        String indexInfo = level == 0 ? " [索引: " + index + "]" : "";
        
        System.out.println(indent + "- " + name + " (" + componentName + ")" + 
                          (type.isEmpty() ? "" : " [type: " + type + "]") + field + indexInfo);
        
        // 递归打印子项
        JsonNode childNode = item.get("child");
        if (childNode != null && childNode.isArray()) {
            for (JsonNode child : childNode) {
                printFormItem(child, level + 1, -1);
            }
        }
    }
}
