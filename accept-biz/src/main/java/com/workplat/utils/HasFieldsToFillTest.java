package com.workplat.utils;

/**
 * hasFieldsToFill 方法测试
 * 验证修复后的字段检测逻辑
 * 
 * <AUTHOR> Assistant
 * @date 2025-06-20
 */
public class HasFieldsToFillTest {
    
    public static void main(String[] args) {
        try {
            // 测试用例1：有字段的表单
            String formWithFields = """
                {
                  "tableName": "testForm",
                  "renderList": [
                    {
                      "name": "表单域",
                      "type": "formArea",
                      "componentName": "ANetFormArea",
                      "props": {
                        "title": "身份证信息",
                        "field": "f2BgEPZ1Q"
                      },
                      "child": [
                        {
                          "name": "输入框",
                          "type": "input",
                          "componentName": "a-net-input",
                          "props": {
                            "field": "sqrxm",
                            "label": "申请人姓名"
                          }
                        }
                      ]
                    }
                  ]
                }
                """;

            // 测试用例2：空表单域（有表单域但没有子字段）
            String formWithEmptyArea = """
                {
                  "tableName": "testForm",
                  "renderList": [
                    {
                      "name": "表单域",
                      "type": "formArea",
                      "componentName": "ANetFormArea",
                      "props": {
                        "title": "身份证信息",
                        "field": "f2BgEPZ1Q"
                      },
                      "child": []
                    }
                  ]
                }
                """;

            // 测试用例3：完全空的表单
            String emptyForm = """
                {
                  "tableName": "testForm",
                  "renderList": []
                }
                """;

            // 测试用例4：有表单域但子组件没有field属性
            String formWithoutFields = """
                {
                  "tableName": "testForm",
                  "renderList": [
                    {
                      "name": "表单域",
                      "type": "formArea",
                      "componentName": "ANetFormArea",
                      "props": {
                        "title": "身份证信息",
                        "field": "f2BgEPZ1Q"
                      },
                      "child": [
                        {
                          "name": "标题",
                          "type": "title",
                          "componentName": "a-net-title",
                          "props": {
                            "label": "仅显示标题"
                          }
                        }
                      ]
                    }
                  ]
                }
                """;

            // 测试用例5：使用FormFieldFilterUtil过滤后的空表单域
            String originalForm = """
                {
                  "tableName": "testForm",
                  "renderList": [
                    {
                      "name": "表单域",
                      "type": "formArea",
                      "componentName": "ANetFormArea",
                      "props": {
                        "title": "身份证信息",
                        "field": "f2BgEPZ1Q"
                      },
                      "child": [
                        {
                          "name": "输入框",
                          "type": "input",
                          "componentName": "a-net-input",
                          "props": {
                            "field": "sqrxm",
                            "label": "申请人姓名"
                          }
                        },
                        {
                          "name": "单选框",
                          "type": "radio",
                          "componentName": "a-net-radio",
                          "props": {
                            "field": "xingbie",
                            "label": "性别"
                          }
                        }
                      ]
                    }
                  ]
                }
                """;

            String fieldMapData = """
                {
                  "otherField": "其他字段"
                }
                """;

            System.out.println("=== hasFieldsToFill 方法测试 ===\n");

            // 测试1
            System.out.println("测试1 - 有字段的表单:");
            boolean result1 = FormStepProcessor.hasFieldsToFill(formWithFields);
            System.out.println("结果: " + result1 + " (期望: true)\n");

            // 测试2
            System.out.println("测试2 - 空表单域:");
            boolean result2 = FormStepProcessor.hasFieldsToFill(formWithEmptyArea);
            System.out.println("结果: " + result2 + " (期望: false)\n");

            // 测试3
            System.out.println("测试3 - 完全空的表单:");
            boolean result3 = FormStepProcessor.hasFieldsToFill(emptyForm);
            System.out.println("结果: " + result3 + " (期望: false)\n");

            // 测试4
            System.out.println("测试4 - 有表单域但子组件没有field属性:");
            boolean result4 = FormStepProcessor.hasFieldsToFill(formWithoutFields);
            System.out.println("结果: " + result4 + " (期望: false)\n");

            // 测试5
            System.out.println("测试5 - 使用FormFieldFilterUtil过滤后的表单:");
            FormFieldFilterUtil.FormFilterResult filterResult = FormFieldFilterUtil.filterFormWithIndexTracking(
                    originalForm, fieldMapData, true);
            String filteredForm = filterResult.getFilteredFormJson();
            
            System.out.println("过滤结果: " + filterResult);
            boolean result5 = FormStepProcessor.hasFieldsToFill(filteredForm);
            System.out.println("hasFieldsToFill结果: " + result5 + " (期望: false，因为没有匹配的字段)\n");

            // 测试6 - 移除模式
            System.out.println("测试6 - 移除模式过滤:");
            String filledFields = """
                {
                  "sqrxm": "张三"
                }
                """;
            
            FormFieldFilterUtil.FormFilterResult removeResult = FormFieldFilterUtil.filterFormWithIndexTracking(
                    originalForm, filledFields, false);
            String removedForm = removeResult.getFilteredFormJson();
            
            System.out.println("移除结果: " + removeResult);
            boolean result6 = FormStepProcessor.hasFieldsToFill(removedForm);
            System.out.println("hasFieldsToFill结果: " + result6 + " (期望: true，因为还有xingbie字段)\n");

            // 总结
            System.out.println("=== 测试总结 ===");
            System.out.println("测试1 (有字段): " + (result1 ? "✓" : "✗"));
            System.out.println("测试2 (空表单域): " + (!result2 ? "✓" : "✗"));
            System.out.println("测试3 (完全空): " + (!result3 ? "✓" : "✗"));
            System.out.println("测试4 (无field属性): " + (!result4 ? "✓" : "✗"));
            System.out.println("测试5 (过滤后空): " + (!result5 ? "✓" : "✗"));
            System.out.println("测试6 (移除后有字段): " + (result6 ? "✓" : "✗"));

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
