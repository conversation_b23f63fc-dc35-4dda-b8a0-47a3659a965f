package com.workplat.componentEngine.engine;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.gss.application.dubbo.dto.BizInstanceFieldsSubmitDTO;
import com.workplat.gss.application.dubbo.dto.BizInstanceMaterialSubmitDTO;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.service.BizInstanceFieldsService;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.application.dubbo.service.BizInstanceMaterialService;
import com.workplat.gss.application.dubbo.vo.BizInstanceFieldsVO;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialVO;
import com.workplat.utils.ChatCacheUtil;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 信息核验组件
 * @date 2025/06/04
 */
@Slf4j
@Service
public class VerificationComponentEngine extends AbstractComponentEngine {

    private final BizInstanceMaterialService bizInstanceMaterialService;
    private final ChatCacheUtil chatCacheUtil;
    private final BizInstanceFieldsService bizInstanceFieldsService;
    private final BizInstanceInfoService bizInstanceInfoService;

    String CODE = "InfoConfirm";

    public VerificationComponentEngine(BizInstanceMaterialService bizInstanceMaterialService,
                                       ChatCacheUtil chatCacheUtil,
                                       BizInstanceFieldsService bizInstanceFieldsService,
                                       BizInstanceInfoService bizInstanceInfoService) {
        this.bizInstanceMaterialService = bizInstanceMaterialService;
        this.chatCacheUtil = chatCacheUtil;
        this.bizInstanceFieldsService = bizInstanceFieldsService;
        this.bizInstanceInfoService = bizInstanceInfoService;
    }

    @Override
    protected ComponentRunVO doExecute() {
        // 获取实例信息
        BizInstanceInfo bizInstanceInfo = bizInstanceInfoService.queryById(componentDataContext.getInstanceId());

        VerificationData verificationData = VerificationData.builder()
                .instanceId(componentDataContext.getInstanceId())
                .matterName(bizInstanceInfo.getMatterName())
//                .hasSignFile(hasSignFile)
//                .materialVOS(instanceMaterialVOS)
//                .fieldsVO(bizInstanceFieldsVO)
                .build();

        log.info("信息核验组件执行");
        ComponentRunVO.RenderData renderData = ComponentRunVO.RenderData.builder()
                .componentName(CODE)
                .componentInfo(verificationData)
                .build();

        ComponentRunVO vo = new ComponentRunVO();
        List<ComponentRunVO.RenderData> renderDataList =
                Collections.singletonList(renderData);
        vo.setRenderData(renderDataList);
        vo.setTips("信息填写完毕。若信息有误，您可在核对表中进行修正。待您确认无误后再提交。");
        return vo;
    }

    @Override
    public boolean canHandle(ComponentDataContext context) {
        return CODE.equals(context.getConfComponent().getEngineCode());
    }

    @Builder
    @Data
    static class VerificationData {
        private String instanceId;
        private String matterName;
        private Boolean hasSignFile;
        private List<BizInstanceMaterialVO> materialVOS;
        private BizInstanceFieldsVO fieldsVO;
    }

    @Override
    public void fillData(ComponentDataContext componentDataContext) {
        // 申报结束提交
        bizInstanceInfoService.endSubmit(componentDataContext.getInstanceId(), null);
    }


}
