package com.workplat.componentEngine.engine;

import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.componentEngine.vo.InstructionFixedVO;
import com.workplat.gss.application.dubbo.service.BizInstanceGuideService;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.gss.application.dubbo.vo.BizInstanceGuideJsonVO;
import com.workplat.gss.application.dubbo.vo.BizInstanceMaterialFileVO;
import com.workplat.gss.application.dubbo.vo.BizInstanceReadNoticeVO;
import com.workplat.gss.service.item.dubbo.matter.service.ConfMatterPublicService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> cheng
 * @package com.workplat.componentEngine.engine
 * @description 申报须知（配置）组件引擎
 * @date 2025/5/28 14:40
 */
@Service
public class InstructionFixedEngine extends AbstractComponentEngine {

    private final BizInstanceInfoService bizInstanceInfoService;
    private final ConfMatterPublicService confMatterPublicService;
    private final BizInstanceGuideService bizInstanceGuideService;

    String CODE = "ProcessConditions";

    public InstructionFixedEngine(BizInstanceInfoService bizInstanceInfoService,
                                  ConfMatterPublicService confMatterPublicService,
                                  BizInstanceGuideService bizInstanceGuideService) {
        this.bizInstanceInfoService = bizInstanceInfoService;
        this.confMatterPublicService = confMatterPublicService;
        this.bizInstanceGuideService = bizInstanceGuideService;
    }


    @Override
    protected ComponentRunVO doExecute() {
        List<ComponentRunVO.RenderData> renderDataList = new ArrayList<>();
        // 获取当前办件信息
        BizInstanceReadNoticeVO bizInstanceReadNoticeVO = new BizInstanceReadNoticeVO();
        BizInstanceGuideJsonVO bizInstanceGuideJsonVO =
                bizInstanceGuideService.getGuideJson(componentDataContext.getInstanceId());

        // 提取第一个 guideInfo（假设只有一个“申报须知”）
        BizInstanceGuideJsonVO.GuideInfo guideInfoItem = bizInstanceGuideJsonVO.getGuideInfo().stream()
                .filter(item -> "text".equals(item.getType()))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("申报须知未找到"));

        // 拆分 content 字符串为多个条件项
        String[] conditions = guideInfoItem.getContent().split("\n");

        // 动态生成 ConditionItem 列表
        List<InstructionFixedVO.ConditionItem> conditionItems = new ArrayList<>();

        // 获取解释项
        BizInstanceGuideJsonVO.GuideInfo explainItemGroup = bizInstanceGuideJsonVO.getGuideInfo().stream()
                .filter(item -> "explain".equals(item.getType()))
                .findFirst()
                .orElse(null);

        // 拆分换行
        for (String condition : conditions) {
            InstructionFixedVO.ConditionItem item = new InstructionFixedVO.ConditionItem();
            item.setText(condition.trim());

            // 添加解释项
            if (explainItemGroup != null) {
                // 查找匹配的解释项（可根据 condition 中的关键字进行匹配）
                List<InstructionFixedVO.Props> props = explainItemGroup.getExplainList().stream()
                        .filter(explain -> condition.contains(explain.getName()))
                        .map(explain -> new InstructionFixedVO.Props(explain.getName(), explain.getRText()))
                        .toList();

                item.setProps(props);
            }
            conditionItems.add(item);
        }

        // 核心组件 申报须知
        InstructionFixedVO instructionFixedVO = new InstructionFixedVO();
        instructionFixedVO.setTitle("办理条件");
        instructionFixedVO.setList(conditionItems);

        // 组装组件数据
        ComponentRunVO.RenderData renderData = ComponentRunVO.RenderData.builder()
                .componentName(CODE)
                .componentInfo(instructionFixedVO)
                .build();
        // 添加组件数据
        renderDataList.add(renderData);
        // 附件组件组装
        List<BizInstanceMaterialFileVO> fileVOS = new ArrayList<>();
        // 获取附件项
        bizInstanceGuideJsonVO.getGuideInfo().stream()
                .filter(item -> "file".equals(item.getType()))
                .findFirst().ifPresent(fileItemGroup -> fileItemGroup.getFileList().forEach(fileItem -> {
                    BizInstanceMaterialFileVO fileVO = new BizInstanceMaterialFileVO();
                    fileVO.setFileName(fileItem.getFileName());
                    fileVO.setFileId(fileItem.getFileId());
                    fileVOS.add(fileVO);
                }));
        // 如果附件项不为空，组装附件组件数据并添加到组件数据列表中
        if (!fileVOS.isEmpty()){
            ComponentRunVO.RenderData renderDataAttachment = ComponentRunVO.RenderData.builder()
                    .componentName("AttachedFiles")
                    .componentInfo(fileVOS)
                    .build();
            renderDataList.add(renderDataAttachment);
        }

        // 组装返回值
        ComponentRunVO vo = new ComponentRunVO();
        vo.setRenderData(renderDataList);
        vo.setTips("请您仔细阅读办理条件并提前准备材料");
        return vo;
    }

    @Override
    public boolean canHandle(ComponentDataContext context) {
        return CODE.equals(context.getConfComponent().getEngineCode());
    }


}
