package com.workplat.componentEngine.engine;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.workplat.accept.business.chat.vo.ComponentRunVO;
import com.workplat.accept.user.util.GatewayUtils;
import com.workplat.componentEngine.engine.dto.ComponentDataContext;
import com.workplat.gss.application.dubbo.entity.BizInstanceFields;
import com.workplat.gss.application.dubbo.entity.BizInstanceInfo;
import com.workplat.gss.application.dubbo.service.BizInstanceFieldsService;
import com.workplat.gss.application.dubbo.service.BizInstanceInfoService;
import com.workplat.matter.entity.ConfMatterExtend;
import com.workplat.matter.service.ConfMatterExtendService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR> cheng
 * @package com.workplat.componentEngine.engine
 * @description 申报须知（第三方接口获取）组件引擎
 * @date 2025/5/28 14:41
 */
@Slf4j
@Service
public class InstructionRemoteEngine extends AbstractInstructionRemoteEngine {

    public InstructionRemoteEngine(RestTemplate restTemplate,
                                   BizInstanceFieldsService bizInstanceFieldsService,
                                   BizInstanceInfoService bizInstanceInfoService,
                                   ConfMatterExtendService confMatterExtendService) {
        super(restTemplate, bizInstanceFieldsService, bizInstanceInfoService, confMatterExtendService);
    }

    @Override
    protected ComponentRunVO doExecute() {
       return process();
    }
}

